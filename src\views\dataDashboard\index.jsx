import React, { useEffect, useMemo, useRef, useState } from 'react'
import { useRequest } from 'ahooks'
import * as echarts from 'echarts'
import { Button, message, Spin } from 'antd'
import MAP_BG from '@/assets/img/project/map-bg.png'
import { useHistory } from 'react-router-dom'
import Pie<PERSON><PERSON> from 'views/dataDashboard/components/PieChart'
import ReactEcharts from 'echarts-for-react'
import ChinaMap from 'views/dataDashboard/constants/china.json'
import WithWatermark from 'views/dataDashboard/components/WithWatermark'
import {
  getProjectStatistics93647,
  getProjectList93656,
  getUserAuth93674,
} from '@/apis/dataDashboard'

import {
  DASHBOARD_MENU,
  PROJECT_STATUS_CONFIG,
  MAP_CONFIG,
  getProvinceColor,
} from 'views/dataDashboard/config'

const MapChart = () => {
  const history = useHistory()
  const echartsRef = useRef(null)
  const intervalRef = useRef(null)
  const highlightedIndexRef = useRef({ seriesIndex: -1, dataIndex: -1 })
  // 4个直辖市的code
  const specialCodes = [110000, 120000, 310000, 500000]
  // 添加权限检查loading状态
  const [permissionChecking, setPermissionChecking] = useState(true)
  const [hasPermission, setHasPermission] = useState(false)

  // 获取数据
  const handleRedirect = (state) => {
    console.log('state', state)
    sessionStorage.setItem('provinceName', state.name)
    const type = specialCodes.includes(state.id) ? '2' : '1'
    history.push(`/dataDashboard/deploymentOverview/${type}/${state.id}`)
  }

  // 检查用户权限
  useEffect(() => {
    const checkUserAuth = async () => {
      try {
        setPermissionChecking(true)
        const { data } = await getUserAuth93674()
        const { authScope, regionId } = data

        // 如果没有全国权限，跳转到部署概况页面
        if (authScope != 0) {
          // 默认跳转到省级视图
          history.push(`/dataDashboard/deploymentOverview/1/${regionId}`)
        } else {
          // 有权限，设置状态
          setHasPermission(true)
        }
      } catch (error) {
        console.error('获取用户权限失败:', error)
        message.error('获取用户权限失败')
      } finally {
        setPermissionChecking(false)
      }
    }

    checkUserAuth()
  }, [])

  // 使用配置中的菜单数据
  const menu = DASHBOARD_MENU
  const {
    data = {
      projectStatusData: PROJECT_STATUS_CONFIG.STATUS_DATA,
    },
  } = useRequest(async () => {
    const { data } = await getProjectStatistics93647()
    console.log('data', data)
    const {
      projectProvinceList,
      projectRegionInfo: {
        deployNum,
        deploy,
        deployIn,
        toBeDeployed,
        negotiationNum,
        channelList,
        gpuTypeList,
        countyPlatform,
        districtSum,
      },
    } = data

    return {
      provinceData: projectProvinceList.map((el) => {
        return {
          id: el.areaId,
          name: el.areaName,
          countyPlatform: el.countyPlatform,
          districtSum: el.districtSum,
          value: el.countyPlatform,
        }
      }),
      deployNum,
      countyPlatform,
      districtSum,
      projectStatusData: [
        {
          name: '已部署',
          color: '#15D888',
          bgColor: 'rgba(21,216,136,0.15)',
          value: deploy || 0,
        },
        {
          name: '部署中',
          color: '#FFBB33',
          bgColor: 'rgba(255,187,51,0.15)',
          value: deployIn || 0,
        },
        {
          name: '资源下发中',
          color: '#ff9222',
          bgColor: 'rgba(249, 100, 36, 0.15)',
          value: toBeDeployed || 0,
        },
        {
          name: '洽谈中',
          color: '#2277FF',
          bgColor: 'rgba(34,119,255,0.15)',
          value: negotiationNum || 0,
        },
      ],
      channelData: channelList.map((el) => {
        return {
          name: el.typeName,
          value: el.count,
        }
      }),
      gpuTypeData: gpuTypeList.map((el) => {
        return {
          name: el.typeName,
          value: el.count,
        }
      }),
    }
  })
  const {
    provinceData = [],
    projectStatusData = [],
    channelData = [],
    gpuTypeData = [],
    deployNum,
    countyPlatform,
    districtSum,
  } = data || {}
  const status = ['DEPLOYING', 'NOT_DEPLOYED', 'COMPLETED', 'NEGOTIATION']
  const { data: scatterData = [] } = useRequest(async () => {
    const { data } = await getProjectList93656({
      type: '6',
      pageNum: 1,
      pageSize: 1000,
    })
    console.log('项目列表数据', data)
    const { projectList = [] } = data
    const proList = projectList?.map((el) => {
      const longLat = JSON.parse(el?.longLat).longLat.split(',')
      return {
        projectName: el.projectName,
        value: longLat,
        status: status[el.status],
      }
    })
    return proList
  })
  // 加载中国地图数据
  const { loading = true } = useRequest(async () => {
    echarts.registerMap('china', ChinaMap)
  })

  const mapOption = useMemo(() => {
    if (loading) return {}

    // 使用配置中的地图样式
    const areaItemStyle = MAP_CONFIG.AREA_ITEM_STYLE

    const mapData = provinceData.map((item) => {
      const areaColor = getProvinceColor(item.value)
      return {
        name: item.name,
        value: item.value,
        itemStyle: {
          borderColor: '#9bd0ff',
          borderWidth: 0.75,
          areaColor: areaColor,
        },
        emphasis: {
          itemStyle: {
            areaColor: areaColor,
            borderColor: '#56BDFF',
            borderWidth: 1.5,
          },
        },
      }
    })

    return {
      backgroundColor: 'transparent',
      tooltip: {
        className: 'p-6 border-none rounded-2 text-12',
        position: 'top',
        trigger: 'item',
        formatter: function (params) {
          if (params.seriesType === 'effectScatter') {
            const typeMap = {
              COMPLETED: projectStatusData[0],
              NOT_DEPLOYED: projectStatusData[1],
              DEPLOYING: projectStatusData[2],
              NEGOTIATION: projectStatusData[3],
            }
            return `<div class="flex items-center">
            <div class="py-2 px-4 rounded-4 mr-4"
            style="color: ${
              typeMap[params.data.status]?.color
            };background-color: ${
              typeMap[params.data.status]?.bgColor
            }">${typeMap[params.data.status]?.name}</div>${params.data?.projectName}</div>`
          } else if (params.seriesType === 'map') {
            // 显示省份名称和项目数量
            // return `<div class="flex flex-col p-2">
            //   <div class="text-14 font-500">${params.name}</div>
            //   <div class="text-12 mt-1">项目数量: ${params.value || 0}</div>
            // </div>`
          }
        },
      },
      geo: [
        {
          map: 'china',
          aspectScale: 0.8,
          zlevel: -1,
          zoom: 1.23,
          layoutCenter: ['50%', '50%'],
          // 如果宽高比大于 1 则宽度为 100，如果小于 1 则高度为 100，保证了不超过 100x100 的区域
          layoutSize: '100%',
          roam: false,
          label: {
            show: false, // 各个省市县的名字
          },
          emphasis: {
            disabled: true,
          },
          itemStyle: areaItemStyle,
        },
        {
          map: 'china',
          aspectScale: 0.8,
          zlevel: -2,
          zoom: 1.23,
          layoutCenter: ['50.5%', '50.8%'],
          // 如果宽高比大于 1 则宽度为 100，如果小于 1 则高度为 100，保证了不超过 100x100 的区域
          layoutSize: '100%',
          roam: false,
          label: {
            show: false, // 各个省市县的名字
          },
          tooltip: {
            show: false,
          },
          emphasis: {
            disabled: true,
          },
          itemStyle: {
            borderColor: 'rgba(0, 142, 255, 0.3)',
            borderWidth: 1,
            areaColor: '#D9DDE4',
            shadowColor: 'rgba(0, 142, 255, 0.4)',
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowOffsetY: 0,
          },
          // 隐藏南海诸岛
          regions: [
            {
              name: '南海诸岛',
              itemStyle: {
                normal: {
                  areaColor: 'rgba(0, 0, 0, 0)',
                  borderColor: 'rgba(0, 0, 0, 0)',
                },
                emphasis: {
                  areaColor: 'rgba(0, 0, 0, 0)',
                  borderColor: 'rgba(0, 0, 0, 0)',
                },
              },
            },
          ],
        },
      ],
      series: [
        {
          name: '全国地图',
          type: 'map',
          mapType: 'china',
          layoutCenter: ['50%', '50%'],
          layoutSize: '100%',
          aspectScale: 0.8, // 长宽比
          roam: false, // 是否开启鼠标缩放和平移漫游。默认不开启。如果只想要开启缩放或者平移，可以设置成 `'scale'` 或者 `'move'`。设置成 `true` 为都开启
          zoom: 1.23, // 当前视角的缩放比例,
          data: mapData, // Add province data for highlighting
          label: {
            show: false,
          },
          selectedMode: false, // 选中模式，默认关闭
          tooltip: {
            show: true,
            trigger: 'item',
          },
          emphasis: {
            label: {
              show: false,
            },
          },
          select: {
            itemStyle: {
              areaColor: 'rgba(21, 185, 237, 0.6)',
              borderColor: '#56BDFF',
              borderWidth: 2,
            },
            label: {
              show: true,
            },
          },
          itemStyle: areaItemStyle,
        },
        // 使用配置中的状态数据动态生成散点图系列
        ...PROJECT_STATUS_CONFIG.STATUS_DATA.map((statusItem) => {
          // 获取状态对应的颜色，去掉#号
          const colorHex = statusItem.color.replace('#', '')
          // 生成rgba格式的颜色用于涟漪效果
          const rgbaColor = `rgba(${parseInt(colorHex.substring(0, 2), 16)},${parseInt(colorHex.substring(2, 4), 16)},${parseInt(colorHex.substring(4, 6), 16)},0.4)`

          return {
            name: statusItem.name,
            type: 'effectScatter',
            coordinateSystem: 'geo',
            showEffectOn: 'emphasis',
            data: scatterData.filter(
              (item) => item.status === statusItem.statusKey,
            ),
            symbolSize: MAP_CONFIG.SCATTER_EFFECT.symbolSize,
            label: {
              normal: {
                show: false,
              },
              emphasis: {
                show: false,
              },
            },
            rippleEffect: {
              color: rgbaColor,
              brushType: MAP_CONFIG.SCATTER_EFFECT.brushType,
              period: MAP_CONFIG.SCATTER_EFFECT.period,
              scale: MAP_CONFIG.SCATTER_EFFECT.scale,
            },
            itemStyle: {
              normal: {
                color: statusItem.color,
                borderColor: '#fff',
                borderWidth: 2,
              },
            },
            emphasis: {},
          }
        }),
      ],
    }
  }, [scatterData, loading, provinceData])

  const clearHighlight = () => {
    const echartsInstance = echartsRef.current?.getEchartsInstance()
    if (echartsInstance && highlightedIndexRef.current.seriesIndex !== -1) {
      echartsInstance.dispatchAction({
        type: 'downplay',
        seriesIndex: highlightedIndexRef.current.seriesIndex,
        dataIndex: highlightedIndexRef.current.dataIndex,
      })
      highlightedIndexRef.current = { seriesIndex: -1, dataIndex: -1 }
    }
  }

  const startRandomHighlight = () => {
    clearInterval(intervalRef.current)
    intervalRef.current = setInterval(() => {
      const echartsInstance = echartsRef.current?.getEchartsInstance()
      if (
        !echartsInstance ||
        loading ||
        !scatterData ||
        scatterData.length === 0
      )
        return

      clearHighlight()
      const availableSeries = mapOption.series
        .map((s, index) => ({ ...s, originalIndex: index }))
        .filter(
          (s) => s.type === 'effectScatter' && s.data && s.data.length > 0,
        )

      if (availableSeries.length === 0) return
      const randomSeriesIndex = Math.floor(
        Math.random() * availableSeries.length,
      )
      const selectedSeries = availableSeries[randomSeriesIndex]
      const randomDataIndex = Math.floor(
        Math.random() * selectedSeries.data.length,
      )
      echartsInstance.dispatchAction({
        type: 'highlight',
        seriesIndex: selectedSeries.originalIndex,
        dataIndex: randomDataIndex,
      })
      echartsInstance.dispatchAction({
        type: 'showTip',
        seriesIndex: selectedSeries.originalIndex,
        dataIndex: randomDataIndex,
      })

      highlightedIndexRef.current = {
        seriesIndex: selectedSeries.originalIndex,
        dataIndex: randomDataIndex,
      }
    }, 3000)
  }

  useEffect(() => {
    if (!loading && echartsRef.current) {
      startRandomHighlight()
    }
    return () => {
      clearInterval(intervalRef.current)
      clearHighlight()
    }
  }, [loading, scatterData, mapOption])
  const onEvents = {
    mouseover: (params) => {
      const echartsInstance = echartsRef.current?.getEchartsInstance()
      if (!echartsInstance) return

      // 处理散点图和地图的鼠标悬停事件
      if (params.componentType === 'series') {
        clearInterval(intervalRef.current)
        clearHighlight()
        echartsInstance.dispatchAction({
          type: 'showTip',
          seriesIndex: params.seriesIndex,
          dataIndex: params.dataIndex,
        })
        echartsInstance.dispatchAction({
          type: 'highlight',
          seriesIndex: params.seriesIndex,
          dataIndex: params.dataIndex,
        })

        highlightedIndexRef.current = {
          seriesIndex: params.seriesIndex,
          dataIndex: params.dataIndex,
        }
      }
    },
    mouseout: (params) => {
      const echartsInstance = echartsRef.current?.getEchartsInstance()
      if (!echartsInstance) return

      // 处理散点图和地图的鼠标移出事件
      if (params.componentType === 'series') {
        echartsInstance.dispatchAction({
          type: 'downplay',
          seriesIndex: params.seriesIndex,
          dataIndex: params.dataIndex,
        })
        echartsInstance.dispatchAction({
          type: 'hideTip',
        })
        startRandomHighlight()
      }
    },
  }

  return (
    <div className="relative w-full h-full min-h-620 overflow-hidden flex justify-center items-center bg-white">
      {permissionChecking ? (
        <Spin size="large" tip="正在检查权限..." />
      ) : hasPermission ? (
        <>
          {/* 地图背景 */}
          <img
            className="absolute z-0 h-full left-50% top-55% -translate-x-1/2 -translate-y-1/2"
            style={{ height: 'calc(100% - 135px)' }}
            src={MAP_BG}
            alt=""
          />
          {/* 菜单 */}
          <div className="absolute flex w-auto h-40 z-9 top-16 right-16">
            {menu.map((item, index) => (
              <div
                key={index}
                className="p-4"
                onClick={() => {
                  history.push(item.path)
                }}
              >
                <Button type="default" disabled={item.disabled}>
                  {item.name}
                </Button>
              </div>
            ))}
          </div>
          {/* 全国数据看板--项目部署总计 */}
          <div className="absolute z-3 top-16 left-40">
            <div className="flex gap-80">
              <div className="flex-col gap-12">
                <div className="text-#262A30 text-18 font-500">
                  全国数据看板
                </div>
                <div className="text-#262A30 text-16 font-500">
                  项目部署总计
                </div>
                <div className="leading-[1] text-32 font-500 text-#2277FF">
                  {deployNum}
                </div>
              </div>
            </div>
            <div className="flex-col gap-16 mt-40">
              {projectStatusData.map((item) => (
                <div key={item.name} className="flex items-center mr-6">
                  <div
                    className="w-6 h-6 rounded-full mr-10"
                    style={{ backgroundColor: item.color }}
                  />
                  <span className="text-16 w-80 text-#262A30">{item.name}</span>
                  <span className="text-16 text-#3A3A3A ml-30">
                    {item.value}
                  </span>
                </div>
              ))}
            </div>
          </div>
          {/* 项目渠道&算力资源类型 */}
          <div className="absolute z-3 left-40 bottom-40 flex">
            <div className="flex-col mr-48">
              <div className="font-bold text-16 text-#262A30">项目渠道</div>
              <PieChart data={channelData} />
            </div>
            <div className="flex-col">
              <div className="font-bold text-16 text-#262A30">算力资源类型</div>
              <PieChart data={gpuTypeData} />
            </div>
          </div>
          <div className="absolute z-3 top-80 right-40">
            <div className="flex-col gap-12">
              <div className="text-#262A30 text-16 font-500">已部署省份</div>
              <div className="leading-[1] text-22">
                <span className="text-#2277FF text-32 font-500">
                  {countyPlatform}
                </span>
                /<span>{districtSum}</span>
              </div>
            </div>
            <div className="text-16 text-#262A30 font-500 mt-24 leading-[1.2]">
              各省份部署情况
            </div>
            <ul
              className="flex w-200 overflow-y-auto mt-16 flex-col"
              style={{ height: 'calc(100vh - 440px)' }}
            >
              {provinceData.map((item) => (
                <li
                  key={item.name}
                  onClick={() => {
                    handleRedirect(item)
                  }}
                  className="flex justify-between leading-[1.2] text-14 py-8 px-4 text-#262A30 cursor-pointer hover:bg-#F6F8FD hover:color-#2277FF"
                >
                  <span className="basis-[40%] line-clamp-1">{item.name}</span>
                  <span className="basis-[60%] line-clamp-1 flex items-center">
                    <div className="w-[40px] h-6 bg-[#eee] mx-12px relative rounded-4 overflow-hidden">
                      <div
                        className="absolute h-6 bg-[#2277FF]"
                        style={{
                          width: `${(item.countyPlatform / item.districtSum) * 100}%`,
                        }}
                      ></div>
                    </div>
                    <div>
                      {item.countyPlatform} / {item.districtSum}
                    </div>
                  </span>
                  {/*<span className="basis-[33%] line-clamp-1">{item.unit}</span>*/}
                </li>
              ))}
            </ul>
          </div>
          {/* 地图 */}
          <ReactEcharts
            ref={echartsRef}
            loading={loading}
            option={mapOption}
            className="relative z-2 w-[70%] !h-[70%]"
            onEvents={onEvents}
          />
        </>
      ) : null}
    </div>
  )
}

// 使用水印高阶组件包装MapChart
export default WithWatermark(MapChart)
